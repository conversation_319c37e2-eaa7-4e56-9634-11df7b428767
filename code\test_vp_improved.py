"""
Improved Vp prediction script with proper scaling and normalization
"""
import os
import argparse
import torch
import numpy as np
import h5py
from torch.utils.data import DataLoader

from vp_model_improved import MWLT_Vp_Small, MWLT_Vp_Base, MWLT_Vp_Large, VpDataNormalizer
from utils import get_device, load_checkpoint, cal_RMSE, cal_R2
from las_processor import LASProcessor

def predict_vp_improved(model_path, input_files=None, device=0):
    """
    Improved Vp prediction with proper scaling
    """
    device = get_device(device)
    normalizer = VpDataNormalizer()
    processor = LASProcessor()
    
    # Default input files
    if input_files is None:
        input_files = ['../A1.hdf5', '../A2.hdf5', '../las_test_data/WELL_001.hdf5']
    
    # Load improved model
    print(f"Loading improved Vp model from {model_path}")
    model = MWLT_Vp_Base()  # Adjust based on your trained model
    model = model.to(device)
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    results = []
    
    for file_path in input_files:
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found, skipping...")
            continue
            
        print(f"\nProcessing {file_path}...")
        
        # Load and process data
        curves = processor.process_hdf5_to_las_format(file_path)
        
        # Prepare input features with proper normalization
        input_features = []
        for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
            if curve_name in curves:
                data = torch.FloatTensor(curves[curve_name])
                if curve_name == 'RLLD':
                    # Log transform for resistivity
                    data = torch.log10(torch.clamp(data, min=0.1))
                    normalized = (data - 1.0) / 2.0
                else:
                    stats = normalizer.input_stats[curve_name]
                    normalized = (data - stats['mean']) / stats['std']
                    normalized = torch.clamp(normalized, -3, 3) / 3
                input_features.append(normalized)
            else:
                print(f"Warning: {curve_name} not found, using zeros")
                input_features.append(torch.zeros(len(curves['AC'])))
        
        # Stack and prepare for model
        input_tensor = torch.stack(input_features).unsqueeze(0).to(device)  # [1, 4, seq_len]
        
        # Ensure correct sequence length
        target_length = 640
        if input_tensor.shape[2] > target_length:
            input_tensor = input_tensor[:, :, :target_length]
        elif input_tensor.shape[2] < target_length:
            # Pad if necessary
            padding = target_length - input_tensor.shape[2]
            input_tensor = torch.nn.functional.pad(input_tensor, (0, padding))
        
        # Predict
        with torch.no_grad():
            prediction = model(input_tensor)
            prediction = prediction.cpu().numpy().flatten()
        
        # Get actual values for comparison
        actual_vp = curves.get('AC', np.zeros_like(prediction))
        if len(actual_vp) > len(prediction):
            actual_vp = actual_vp[:len(prediction)]
        elif len(actual_vp) < len(prediction):
            prediction = prediction[:len(actual_vp)]
        
        # Calculate metrics
        rmse = cal_RMSE(prediction, actual_vp)
        r2 = cal_R2(prediction, actual_vp)
        
        result = {
            'file': os.path.basename(file_path),
            'rmse': rmse,
            'r2': r2,
            'pred_range': [prediction.min(), prediction.max()],
            'actual_range': [actual_vp.min(), actual_vp.max()],
            'prediction': prediction,
            'actual': actual_vp
        }
        results.append(result)
        
        print(f"  RMSE: {rmse:.2f}")
        print(f"  R²: {r2:.4f}")
        print(f"  Predicted range: [{prediction.min():.2f}, {prediction.max():.2f}]")
        print(f"  Actual range: [{actual_vp.min():.2f}, {actual_vp.max():.2f}]")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Improved Vp prediction")
    parser.add_argument("--model_path", type=str, 
                       default="../vp_improved_training/best_vp_improved_model.pth",
                       help="Path to improved Vp model")
    parser.add_argument("--device", type=int, default=0, help="GPU device ID")
    parser.add_argument("--input_files", nargs='+', default=None,
                       help="Input files to process")
    
    args = parser.parse_args()
    
    print("=== Improved Vp Prediction Test ===")
    
    if not os.path.exists(args.model_path):
        print(f"❌ Model not found: {args.model_path}")
        print("Please train the improved model first:")
        print("  python train_vp_improved.py")
        return
    
    results = predict_vp_improved(args.model_path, args.input_files, args.device)
    
    if results:
        print("\n=== Summary ===")
        avg_rmse = np.mean([r['rmse'] for r in results])
        avg_r2 = np.mean([r['r2'] for r in results])
        print(f"Average RMSE: {avg_rmse:.2f}")
        print(f"Average R²: {avg_r2:.4f}")
        
        print("\n=== Next Steps ===")
        if avg_rmse > 50:
            print("⚠️  RMSE still high. Consider:")
            print("  - More training data")
            print("  - Longer training (more epochs)")
            print("  - Different model architecture")
        else:
            print("✅ Good performance! Model is working well.")

if __name__ == "__main__":
    main()
