@echo off
echo ========================================
echo    MWLT Transformer Test Suite
echo ========================================
echo Current directory: %CD%

REM Change to the code directory
cd /d "%~dp0"

echo.
echo Available Test Options:
echo 1. Standard Density Prediction Test (Original)
echo 2. Vp (Sonic Velocity) Prediction Test (New)
echo 3. Device Detection Test
echo 4. Quick Vp Training Test
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto standard_test
if "%choice%"=="2" goto vp_test
if "%choice%"=="3" goto device_test
if "%choice%"=="4" goto quick_training
if "%choice%"=="5" goto exit
echo Invalid choice. Running standard test...

:standard_test
echo.
echo ========================================
echo Running Standard Density Prediction Test
echo ========================================
echo Input: GR, AC, CNL, RLLD
echo Output: DEN (Density)
echo Model: Base model trained for density prediction
echo.
python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth" --input_curves ["GR","AC","CNL","RLLD"] --output_curves ["DEN"]
echo.
echo Test completed. Check results in ../result_base1/pred_val/
goto end

:vp_test
echo.
echo ========================================
echo Running Vp (Sonic Velocity) Prediction Test
echo ========================================
echo Input: A1.hdf5, A2.hdf5, and synthetic LAS data
echo Output: Vp (Sonic velocity) predictions
echo Model: Transformer model with GPU/CPU auto-detection
echo.
python test_vp_prediction.py
echo.
echo Vp test completed. Check results in ../vp_prediction_results/
echo.
echo Running results analysis...
python analyze_vp_results.py
goto end

:device_test
echo.
echo ========================================
echo Running Device Detection Test
echo ========================================
echo Testing GPU/CPU auto-detection capabilities
echo.
python test_device_detection.py
goto end

:quick_training
echo.
echo ========================================
echo Running Quick Vp Training Test
echo ========================================
echo Training a simple Vp prediction model (30 epochs)
echo This will take approximately 5 minutes
echo.
python simple_train_vp.py
echo.
echo Quick training completed. Check results in ../simple_vp_training/
goto end

:exit
echo Exiting...
goto end

:end
echo.
echo ========================================
echo Additional Commands You Can Try:
echo ========================================
echo For Vp prediction with specific file:
echo   python test_vp_prediction.py --input_file ../A1.hdf5
echo.
echo For training monitoring:
echo   python monitor_vp_training.py monitor
echo.
echo For transfer learning:
echo   python quick_train_vp.py transfer_learning
echo.
echo For full training from scratch:
echo   python quick_train_vp.py from_scratch
echo ========================================
pause
